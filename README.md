# مشغل الوسائط المتعددة 🎬

تطبيق سطح مكتب خفيف وأنيق لتشغيل ملفات الفيديو والصوت باستخدام Python و PyQt6 و VLC.

## المميزات ✨

- 🎥 **تشغيل الفيديو**: دعم جميع صيغ الفيديو الشائعة (MP4, AVI, MKV, MOV, WMV, FLV)
- 🎵 **تشغيل الصوت**: دعم ملفات الصوت (MP3, WAV, AAC, OGG, M4A)
- 🖥️ **عرض مدمج**: مشغل فيديو مدمج داخل النافذة
- 🎛️ **تحكم كامل**: أزرار تشغيل، إيقاف، وإيقاف مؤقت
- 📊 **شريط تقدم**: عرض وتحكم في موضع التشغيل
- 🌙 **تصميم داكن**: واجهة أنيقة ومريحة للعين
- 📱 **متجاوب**: مناسب للشاشات الصغيرة والكبيرة
- ⚡ **خفيف**: استهلاك قليل للذاكرة والمعالج

## متطلبات النظام 🔧

- **نظام التشغيل**: Windows 10/11, macOS, Linux
- **Python**: 3.8 أو أحدث
- **VLC Media Player**: يجب تثبيته على النظام

## التثبيت 📦

### 1. تثبيت VLC Media Player
قم بتحميل وتثبيت VLC من الموقع الرسمي:
- Windows: https://www.videolan.org/vlc/download-windows.html
- macOS: https://www.videolan.org/vlc/download-macosx.html
- Linux: `sudo apt install vlc` (Ubuntu/Debian)

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

أو تثبيت المكتبات يدوياً:
```bash
pip install PyQt6 python-vlc pyinstaller
```

## الاستخدام 🚀

### تشغيل التطبيق

#### الطريقة الأولى: استخدام ملف التشغيل التلقائي (مُوصى به)
```bash
# Windows
run_app.bat

# أو مباشرة
double-click run_app.bat
```

#### الطريقة الثانية: تشغيل مباشر
```bash
# إذا كان VLC مثبت
python media_player.py

# إصدار تجريبي (بدون VLC)
python media_player_demo.py
```

### الواجهة
1. **فتح ملف**: اضغط على زر "📁 فتح ملف" لاختيار ملف وسائط
2. **التشغيل**: اضغط "▶️ تشغيل" لبدء التشغيل أو "⏸️ إيقاف مؤقت" للإيقاف المؤقت
3. **الإيقاف**: اضغط "⏹️ إيقاف" لإيقاف التشغيل تماماً
4. **التنقل**: استخدم شريط التقدم للانتقال إلى موضع معين

## بناء ملف تنفيذي 🔨

لإنشاء ملف تنفيذي (.exe) من التطبيق:

```bash
python build_exe.py
```

سيتم إنشاء ملف `MediaPlayer.exe` يمكن تشغيله بدون الحاجة لتثبيت Python.

### تنظيف ملفات البناء
```bash
python build_exe.py clean
```

## الإصدار التجريبي 🧪

إذا لم تتمكن من تثبيت VLC، يمكنك استخدام الإصدار التجريبي:

```bash
python media_player_demo.py
```

**مميزات الإصدار التجريبي:**
- ✅ واجهة مستخدم كاملة
- ✅ اختيار الملفات
- ✅ أزرار التحكم (محاكاة)
- ✅ شريط التقدم (محاكاة)
- ❌ لا يشغل الملفات فعلياً

**الغرض:** اختبار الواجهة والتأكد من عمل PyQt6 قبل تثبيت VLC.

## الصيغ المدعومة 📋

### ملفات الفيديو
- MP4, AVI, MKV, MOV, WMV, FLV
- MPEG, 3GP, WEBM, OGV

### ملفات الصوت
- MP3, WAV, AAC, OGG, M4A
- FLAC, WMA, APE

## استكشاف الأخطاء 🔍

### مشكلة: "فشل في تهيئة مشغل VLC"
**الحل**: تأكد من تثبيت VLC Media Player على النظام

### مشكلة: "لا يظهر الفيديو"
**الحل**:
- تأكد من أن ملف الفيديو غير تالف
- جرب ملف فيديو آخر
- أعد تشغيل التطبيق

### مشكلة: "لا يعمل الصوت"
**الحل**:
- تحقق من إعدادات الصوت في النظام
- تأكد من أن مستوى الصوت ليس صفر
- جرب ملف صوتي آخر

## التطوير 👨‍💻

### هيكل المشروع
```
lewyer/
├── media_player.py          # التطبيق الرئيسي (مع VLC)
├── media_player_demo.py     # الإصدار التجريبي (بدون VLC)
├── requirements.txt         # المتطلبات
├── setup.py                # إعدادات التحزيم
├── build_exe.py            # سكريبت بناء الملف التنفيذي
├── pyinstaller_config.spec # تكوين PyInstaller
├── run_app.bat             # تشغيل تلقائي (Windows)
├── install_requirements.bat # تثبيت المتطلبات (Windows)
├── install_vlc_guide.md    # دليل تثبيت VLC
└── README.md               # هذا الملف
```

### إضافة مميزات جديدة
يمكنك تطوير التطبيق بإضافة:
- قائمة تشغيل
- تحكم في مستوى الصوت
- ترجمات
- مرشحات فيديو
- اختصارات لوحة المفاتيح

## الترخيص 📄

هذا المشروع مفتوح المصدر ومتاح تحت رخصة MIT.

## المساهمة 🤝

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الدعم 💬

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح Issue في المشروع.

---

**استمتع بتشغيل الوسائط! 🎉**
