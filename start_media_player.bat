@echo off
echo ========================================
echo 🎬 مشغل الوسائط المتعددة
echo ========================================
echo.

echo 🚀 تشغيل مشغل الوسائط...
echo.

if exist "MediaPlayer.exe" (
    echo ✅ تم العثور على الملف التنفيذي
    echo 🎬 تشغيل MediaPlayer.exe...
    start "" "MediaPlayer.exe"
    echo.
    echo ✅ تم تشغيل التطبيق بنجاح!
    echo يمكنك الآن إغلاق هذه النافذة.
) else (
    echo ❌ لم يتم العثور على MediaPlayer.exe
    echo.
    echo يرجى التأكد من وجود الملف في نفس المجلد
    echo أو تشغيل build_exe.py لإنشاء الملف التنفيذي
    echo.
    pause
)

echo.
echo 👋 شكراً لاستخدام مشغل الوسائط المتعددة!
