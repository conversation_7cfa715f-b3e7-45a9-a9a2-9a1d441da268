#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف الإعداد لتحزيم مشغل الوسائط كملف تنفيذي
"""

from setuptools import setup, find_packages

setup(
    name="media-player",
    version="1.0.0",
    description="مشغل وسائط متعددة خفيف باستخدام PyQt6 و VLC",
    author="Developer",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "PyQt6>=6.6.1",
        "python-vlc>=3.0.18122",
    ],
    entry_points={
        'console_scripts': [
            'media-player=media_player:main',
        ],
    },
    python_requires=">=3.8",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Multimedia :: Video :: Display",
        "Topic :: Multimedia :: Sound/Audio :: Players",
    ],
)
