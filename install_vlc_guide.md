# دليل تثبيت VLC Media Player 🎬

لتشغيل مشغل الوسائط المتعددة، تحتاج إلى تثبيت VLC Media Player أولاً.

## Windows 🪟

### الطريقة الأولى: التحميل من الموقع الرسمي
1. اذهب إلى: https://www.videolan.org/vlc/download-windows.html
2. اضغط على "Download VLC"
3. شغل الملف المحمل واتبع التعليمات
4. تأكد من تحديد "Add VLC to PATH" أثناء التثبيت

### الطريقة الثانية: باستخدام Chocolatey
```powershell
choco install vlc
```

### الطريقة الثالثة: باستخدام winget
```powershell
winget install VideoLAN.VLC
```

## macOS 🍎

### الطريقة الأولى: التحميل من الموقع الرسمي
1. اذه<PERSON> إلى: https://www.videolan.org/vlc/download-macosx.html
2. حمل ملف .dmg
3. اسحب VLC إلى مجلد Applications

### الطريقة الثانية: باستخدام Homebrew
```bash
brew install --cask vlc
```

## Linux 🐧

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install vlc
```

### CentOS/RHEL/Fedora
```bash
# Fedora
sudo dnf install vlc

# CentOS/RHEL (تحتاج EPEL repository)
sudo yum install epel-release
sudo yum install vlc
```

### Arch Linux
```bash
sudo pacman -S vlc
```

## التحقق من التثبيت ✅

بعد تثبيت VLC، تحقق من أنه يعمل:

### Windows
```cmd
vlc --version
```

### macOS/Linux
```bash
vlc --version
```

## تثبيت python-vlc 🐍

بعد تثبيت VLC Media Player، ثبت مكتبة Python:

```bash
pip install python-vlc
```

## استكشاف الأخطاء 🔧

### مشكلة: "VLC غير متوفر"
**الحل**: تأكد من تثبيت VLC Media Player أولاً

### مشكلة: "python-vlc لا يعمل"
**الحلول**:
1. تأكد من تثبيت VLC في المسار الافتراضي
2. أعد تشغيل الكمبيوتر بعد تثبيت VLC
3. جرب إعادة تثبيت python-vlc:
   ```bash
   pip uninstall python-vlc
   pip install python-vlc
   ```

### مشكلة: "لا يمكن العثور على VLC"
**Windows**: تأكد من إضافة VLC إلى PATH:
1. ابحث عن "Environment Variables"
2. أضف مجلد VLC (عادة `C:\Program Files\VideoLAN\VLC`) إلى PATH

### مشكلة: "خطأ في تهيئة VLC"
**الحلول**:
1. تأكد من أن VLC يعمل بشكل منفصل
2. جرب تشغيل التطبيق كمدير (Run as Administrator)
3. تحقق من أن إصدار VLC متوافق (يُنصح بالإصدار 3.0 أو أحدث)

## إصدارات VLC المدعومة 📋

- **الحد الأدنى**: VLC 3.0
- **المُوصى به**: VLC 3.0.18 أو أحدث
- **أحدث إصدار**: VLC 3.0.20

## ملاحظات مهمة ⚠️

1. **Windows**: تأكد من تطابق معمارية VLC مع Python (32-bit أو 64-bit)
2. **macOS**: قد تحتاج إلى السماح للتطبيق في إعدادات الأمان
3. **Linux**: تأكد من تثبيت حزم التطوير إذا كنت تستخدم توزيعة قديمة

## اختبار التثبيت 🧪

لاختبار أن كل شيء يعمل بشكل صحيح:

```python
import vlc
print("VLC متوفر ويعمل بشكل صحيح!")
```

إذا لم تظهر أي أخطاء، فأنت جاهز لتشغيل مشغل الوسائط!
