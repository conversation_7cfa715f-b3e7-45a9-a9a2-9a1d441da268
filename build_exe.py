#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لبناء ملف تنفيذي من التطبيق باستخدام PyInstaller
"""

import os
import sys
import subprocess
import shutil

def build_executable():
    """بناء الملف التنفيذي"""
    
    print("🔨 بدء عملية بناء الملف التنفيذي...")
    
    # التأكد من وجود PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller متوفر")
    except ImportError:
        print("❌ PyInstaller غير مثبت. يرجى تثبيته أولاً:")
        print("pip install pyinstaller")
        return False
    
    # إعدادات PyInstaller
    cmd = [
        "pyinstaller",
        "--onefile",                    # ملف واحد
        "--windowed",                   # بدون نافذة وحدة التحكم
        "--name=MediaPlayer",           # اسم الملف التنفيذي
        "--icon=icon.ico",              # أيقونة (اختيارية)
        "--add-data=requirements.txt;.", # إضافة ملفات إضافية
        "--hidden-import=vlc",          # استيراد مخفي لـ VLC
        "--hidden-import=PyQt6",        # استيراد مخفي لـ PyQt6
        "--clean",                      # تنظيف الملفات المؤقتة
        "media_player.py"               # الملف الرئيسي
    ]
    
    # إزالة أيقونة إذا لم تكن موجودة
    if not os.path.exists("icon.ico"):
        cmd.remove("--icon=icon.ico")
    
    try:
        print("🔄 تشغيل PyInstaller...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ تم بناء الملف التنفيذي بنجاح!")
        
        # نسخ الملف التنفيذي إلى المجلد الجذر
        exe_path = os.path.join("dist", "MediaPlayer.exe")
        if os.path.exists(exe_path):
            shutil.copy2(exe_path, "MediaPlayer.exe")
            print(f"📁 تم نسخ الملف التنفيذي إلى: MediaPlayer.exe")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في بناء الملف التنفيذي:")
        print(f"خطأ: {e}")
        if e.stdout:
            print(f"المخرجات: {e.stdout}")
        if e.stderr:
            print(f"الأخطاء: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def clean_build_files():
    """تنظيف ملفات البناء المؤقتة"""
    dirs_to_remove = ["build", "dist", "__pycache__"]
    files_to_remove = ["MediaPlayer.spec"]
    
    print("🧹 تنظيف الملفات المؤقتة...")
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🗑️ تم حذف المجلد: {dir_name}")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"🗑️ تم حذف الملف: {file_name}")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🎬 مشغل الوسائط المتعددة - أداة البناء")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "clean":
        clean_build_files()
        return
    
    # بناء الملف التنفيذي
    success = build_executable()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 تم بناء التطبيق بنجاح!")
        print("📁 يمكنك العثور على الملف التنفيذي في: MediaPlayer.exe")
        print("=" * 50)
        
        # سؤال المستخدم عن تنظيف الملفات المؤقتة
        try:
            response = input("\n🧹 هل تريد تنظيف الملفات المؤقتة؟ (y/n): ").lower()
            if response in ['y', 'yes', 'نعم']:
                clean_build_files()
        except KeyboardInterrupt:
            print("\n👋 تم إلغاء العملية")
    else:
        print("\n" + "=" * 50)
        print("❌ فشل في بناء التطبيق")
        print("يرجى التحقق من الأخطاء أعلاه")
        print("=" * 50)

if __name__ == "__main__":
    main()
