@echo off
echo ========================================
echo تشغيل مشغل الوسائط المتعددة
echo ========================================
echo.

echo جاري البحث عن Python...

:: البحث عن Python في المسارات المختلفة
set PYTHON_EXE=
if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe" (
    set PYTHON_EXE=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
) else if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" (
    set PYTHON_EXE=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
) else (
    python --version >nul 2>&1
    if not errorlevel 1 (
        set PYTHON_EXE=python
    )
)

if "%PYTHON_EXE%"=="" (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تحميل وتثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ تم العثور على Python: %PYTHON_EXE%

echo جاري التحقق من المتطلبات...
%PYTHON_EXE% -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo جاري تثبيت PyQt6...
    %PYTHON_EXE% -m pip install PyQt6
    if errorlevel 1 (
        echo فشل في تثبيت PyQt6
        pause
        exit /b 1
    )
)

echo ✅ PyQt6 متوفر

:: التحقق من VLC
%PYTHON_EXE% -c "import vlc" >nul 2>&1
if errorlevel 1 (
    echo تحذير: VLC غير متوفر - سيتم تشغيل الإصدار التجريبي
    echo.
    echo 🚀 تشغيل الإصدار التجريبي...
    %PYTHON_EXE% media_player_demo.py
) else (
    echo ✅ VLC متوفر
    echo.
    echo 🚀 تشغيل التطبيق الكامل...
    %PYTHON_EXE% media_player.py
)

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    pause
)
