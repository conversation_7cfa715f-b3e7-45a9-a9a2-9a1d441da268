#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل الوسائط المتعددة - Media Player
تطبيق خفيف لتشغيل الفيديو والصوت باستخدام PyQt6 و VLC
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QFileDialog, QFrame,
                             QLabel, QSlider, QMessageBox, QSizePolicy)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPalette, QColor

# محاولة استيراد VLC مع معالجة الأخطاء
try:
    import vlc
    VLC_AVAILABLE = True
except ImportError:
    VLC_AVAILABLE = False
    print("تحذير: مكتبة VLC غير متوفرة. يرجى تثبيت VLC Media Player أولاً.")


class VideoFrame(QFrame):
    """إطار عرض الفيديو"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        self.setStyleSheet("""
            QFrame {
                background-color: #000000;
                border: 2px solid #333333;
                border-radius: 8px;
            }
        """)
        self.setMinimumSize(400, 300)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)


class MediaPlayer(QMainWindow):
    """النافذة الرئيسية لمشغل الوسائط"""

    def __init__(self):
        super().__init__()
        self.media_player = None
        self.instance = None
        self.current_file = None
        self.is_paused = False

        self.init_vlc()
        self.init_ui()
        self.setup_timer()

    def init_vlc(self):
        """تهيئة مشغل VLC"""
        if not VLC_AVAILABLE:
            QMessageBox.critical(
                self,
                "خطأ - VLC غير متوفر",
                "مكتبة VLC غير متوفرة.\n\n"
                "يرجى تثبيت VLC Media Player من:\n"
                "https://www.videolan.org/vlc/\n\n"
                "ثم تثبيت python-vlc:\n"
                "pip install python-vlc"
            )
            sys.exit(1)

        try:
            self.instance = vlc.Instance()
            self.media_player = self.instance.media_player_new()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تهيئة مشغل VLC:\n{str(e)}")
            sys.exit(1)

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("مشغل الوسائط المتعددة")
        self.setGeometry(100, 100, 800, 600)
        self.setMinimumSize(600, 400)

        # تطبيق نمط داكن
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QPushButton {
                background-color: #404040;
                border: 2px solid #555555;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                color: #ffffff;
            }
            QPushButton:hover {
                background-color: #505050;
                border-color: #777777;
            }
            QPushButton:pressed {
                background-color: #353535;
            }
            QPushButton:disabled {
                background-color: #2a2a2a;
                color: #666666;
                border-color: #333333;
            }
            QLabel {
                color: #ffffff;
                font-size: 11px;
            }
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: #404040;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #ffffff;
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QSlider::sub-page:horizontal {
                background: #0078d4;
                border-radius: 4px;
            }
        """)

        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # منطقة عرض الفيديو
        self.video_frame = VideoFrame()
        main_layout.addWidget(self.video_frame, 1)

        # معلومات الملف
        self.file_label = QLabel("لم يتم اختيار ملف")
        self.file_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.file_label.setStyleSheet("font-size: 13px; color: #cccccc; padding: 5px;")
        main_layout.addWidget(self.file_label)

        # شريط التقدم
        self.position_slider = QSlider(Qt.Orientation.Horizontal)
        self.position_slider.setRange(0, 1000)
        self.position_slider.sliderMoved.connect(self.set_position)
        main_layout.addWidget(self.position_slider)

        # أزرار التحكم
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(10)

        # زر فتح الملف
        self.open_button = QPushButton("📁 فتح ملف")
        self.open_button.clicked.connect(self.open_file)
        controls_layout.addWidget(self.open_button)

        controls_layout.addStretch()

        # أزرار التشغيل
        self.play_button = QPushButton("▶️ تشغيل")
        self.play_button.clicked.connect(self.play_pause)
        self.play_button.setEnabled(False)
        controls_layout.addWidget(self.play_button)

        self.stop_button = QPushButton("⏹️ إيقاف")
        self.stop_button.clicked.connect(self.stop)
        self.stop_button.setEnabled(False)
        controls_layout.addWidget(self.stop_button)

        controls_layout.addStretch()

        main_layout.addLayout(controls_layout)

        # ربط مشغل VLC بإطار الفيديو
        if sys.platform.startswith('win'):
            self.media_player.set_hwnd(self.video_frame.winId())
        elif sys.platform == 'darwin':
            self.media_player.set_nsobject(int(self.video_frame.winId()))
        else:
            self.media_player.set_xwindow(self.video_frame.winId())

    def setup_timer(self):
        """إعداد مؤقت تحديث شريط التقدم"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_ui)
        self.timer.start(100)

    def open_file(self):
        """فتح ملف وسائط"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف وسائط",
            "",
            "ملفات الوسائط (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.mp3 *.wav *.aac *.ogg *.m4a);;جميع الملفات (*)"
        )

        if file_path:
            self.load_media(file_path)

    def load_media(self, file_path):
        """تحميل ملف الوسائط"""
        try:
            self.current_file = file_path
            media = self.instance.media_new(file_path)
            self.media_player.set_media(media)

            # تحديث واجهة المستخدم
            file_name = os.path.basename(file_path)
            self.file_label.setText(f"الملف: {file_name}")

            # تفعيل أزرار التحكم
            self.play_button.setEnabled(True)
            self.stop_button.setEnabled(True)

            # إعادة تعيين حالة التشغيل
            self.is_paused = False
            self.play_button.setText("▶️ تشغيل")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الملف:\n{str(e)}")

    def play_pause(self):
        """تشغيل أو إيقاف مؤقت"""
        if self.media_player.get_state() == vlc.State.Playing:
            self.media_player.pause()
            self.is_paused = True
            self.play_button.setText("▶️ تشغيل")
        else:
            self.media_player.play()
            self.is_paused = False
            self.play_button.setText("⏸️ إيقاف مؤقت")

    def stop(self):
        """إيقاف التشغيل"""
        self.media_player.stop()
        self.is_paused = False
        self.play_button.setText("▶️ تشغيل")
        self.position_slider.setValue(0)

    def set_position(self, position):
        """تعيين موضع التشغيل"""
        if self.media_player.get_length() > 0:
            self.media_player.set_position(position / 1000.0)

    def update_ui(self):
        """تحديث واجهة المستخدم"""
        if self.media_player.get_length() > 0:
            # تحديث شريط التقدم
            position = self.media_player.get_position()
            self.position_slider.setValue(int(position * 1000))

            # تحديث حالة أزرار التحكم حسب حالة المشغل
            state = self.media_player.get_state()
            if state == vlc.State.Ended:
                self.is_paused = False
                self.play_button.setText("▶️ تشغيل")

    def closeEvent(self, event):
        """إغلاق التطبيق"""
        if self.media_player:
            self.media_player.stop()
        event.accept()


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setApplicationName("مشغل الوسائط المتعددة")
    app.setApplicationVersion("1.0")

    # تطبيق خط عربي إذا كان متوفراً
    try:
        font = QFont("Segoe UI", 10)
        app.setFont(font)
    except:
        pass

    player = MediaPlayer()
    player.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
