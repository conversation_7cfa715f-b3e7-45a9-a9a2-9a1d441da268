#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل الوسائط المتعددة - إصدار تجريبي
نسخة تجريبية تعمل بدون VLC لاختبار الواجهة
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QFileDialog, QFrame,
                             QLabel, QSlider, QMessageBox, QSizePolicy)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont


class VideoFrame(QFrame):
    """إطار عرض الفيديو"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        self.setStyleSheet("""
            QFrame {
                background-color: #000000;
                border: 2px solid #333333;
                border-radius: 8px;
            }
        """)
        self.setMinimumSize(400, 300)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        # إضافة نص توضيحي
        layout = QVBoxLayout(self)
        demo_label = QLabel("🎬 منطقة عرض الفيديو\n\n(إصدار تجريبي - بدون VLC)")
        demo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        demo_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
            }
        """)
        layout.addWidget(demo_label)


class MediaPlayerDemo(QMainWindow):
    """النافذة الرئيسية لمشغل الوسائط - إصدار تجريبي"""
    
    def __init__(self):
        super().__init__()
        self.current_file = None
        self.is_playing = False
        self.is_paused = False
        self.position = 0
        self.duration = 100  # مدة وهمية للاختبار
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("مشغل الوسائط المتعددة - إصدار تجريبي")
        self.setGeometry(100, 100, 800, 600)
        self.setMinimumSize(600, 400)
        
        # تطبيق نمط داكن
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QPushButton {
                background-color: #404040;
                border: 2px solid #555555;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                color: #ffffff;
            }
            QPushButton:hover {
                background-color: #505050;
                border-color: #777777;
            }
            QPushButton:pressed {
                background-color: #353535;
            }
            QPushButton:disabled {
                background-color: #2a2a2a;
                color: #666666;
                border-color: #333333;
            }
            QLabel {
                color: #ffffff;
                font-size: 11px;
            }
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: #404040;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #ffffff;
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QSlider::sub-page:horizontal {
                background: #0078d4;
                border-radius: 4px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # رسالة تجريبية
        demo_info = QLabel("🚧 هذا إصدار تجريبي من مشغل الوسائط")
        demo_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        demo_info.setStyleSheet("""
            QLabel {
                background-color: #ff6b35;
                color: #ffffff;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 13px;
            }
        """)
        main_layout.addWidget(demo_info)
        
        # منطقة عرض الفيديو
        self.video_frame = VideoFrame()
        main_layout.addWidget(self.video_frame, 1)
        
        # معلومات الملف
        self.file_label = QLabel("لم يتم اختيار ملف")
        self.file_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.file_label.setStyleSheet("font-size: 13px; color: #cccccc; padding: 5px;")
        main_layout.addWidget(self.file_label)
        
        # شريط التقدم
        self.position_slider = QSlider(Qt.Orientation.Horizontal)
        self.position_slider.setRange(0, 100)
        self.position_slider.sliderMoved.connect(self.set_position)
        main_layout.addWidget(self.position_slider)
        
        # أزرار التحكم
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(10)
        
        # زر فتح الملف
        self.open_button = QPushButton("📁 فتح ملف")
        self.open_button.clicked.connect(self.open_file)
        controls_layout.addWidget(self.open_button)
        
        controls_layout.addStretch()
        
        # أزرار التشغيل
        self.play_button = QPushButton("▶️ تشغيل")
        self.play_button.clicked.connect(self.play_pause)
        self.play_button.setEnabled(False)
        controls_layout.addWidget(self.play_button)
        
        self.stop_button = QPushButton("⏹️ إيقاف")
        self.stop_button.clicked.connect(self.stop)
        self.stop_button.setEnabled(False)
        controls_layout.addWidget(self.stop_button)
        
        controls_layout.addStretch()
        
        # زر معلومات
        info_button = QPushButton("ℹ️ معلومات")
        info_button.clicked.connect(self.show_info)
        controls_layout.addWidget(info_button)
        
        main_layout.addLayout(controls_layout)
    
    def setup_timer(self):
        """إعداد مؤقت تحديث شريط التقدم"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_ui)
        self.timer.start(100)
    
    def open_file(self):
        """فتح ملف وسائط"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف وسائط",
            "",
            "ملفات الوسائط (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.mp3 *.wav *.aac *.ogg *.m4a);;جميع الملفات (*)"
        )
        
        if file_path:
            self.load_media(file_path)
    
    def load_media(self, file_path):
        """تحميل ملف الوسائط (محاكاة)"""
        self.current_file = file_path
        file_name = os.path.basename(file_path)
        self.file_label.setText(f"الملف: {file_name}")
        
        # تفعيل أزرار التحكم
        self.play_button.setEnabled(True)
        self.stop_button.setEnabled(True)
        
        # إعادة تعيين حالة التشغيل
        self.is_playing = False
        self.is_paused = False
        self.position = 0
        self.play_button.setText("▶️ تشغيل")
        self.position_slider.setValue(0)
        
        QMessageBox.information(
            self, 
            "ملف محمل", 
            f"تم تحميل الملف:\n{file_name}\n\n"
            "ملاحظة: هذا إصدار تجريبي ولا يشغل الملفات فعلياً.\n"
            "لتشغيل الملفات، يرجى تثبيت VLC واستخدام media_player.py"
        )
    
    def play_pause(self):
        """تشغيل أو إيقاف مؤقت (محاكاة)"""
        if self.is_playing:
            self.is_playing = False
            self.is_paused = True
            self.play_button.setText("▶️ تشغيل")
        else:
            self.is_playing = True
            self.is_paused = False
            self.play_button.setText("⏸️ إيقاف مؤقت")
    
    def stop(self):
        """إيقاف التشغيل (محاكاة)"""
        self.is_playing = False
        self.is_paused = False
        self.position = 0
        self.play_button.setText("▶️ تشغيل")
        self.position_slider.setValue(0)
    
    def set_position(self, position):
        """تعيين موضع التشغيل (محاكاة)"""
        self.position = position
    
    def update_ui(self):
        """تحديث واجهة المستخدم"""
        if self.is_playing and self.current_file:
            # محاكاة تقدم التشغيل
            self.position += 1
            if self.position >= self.duration:
                self.position = self.duration
                self.stop()
            
            self.position_slider.setValue(self.position)
    
    def show_info(self):
        """عرض معلومات التطبيق"""
        QMessageBox.information(
            self,
            "معلومات التطبيق",
            "🎬 مشغل الوسائط المتعددة\n"
            "الإصدار: 1.0 (تجريبي)\n\n"
            "هذا إصدار تجريبي يعرض الواجهة فقط.\n"
            "لتشغيل الملفات فعلياً:\n\n"
            "1. ثبت VLC Media Player\n"
            "2. ثبت python-vlc\n"
            "3. استخدم media_player.py\n\n"
            "راجع install_vlc_guide.md للتفاصيل"
        )
    
    def closeEvent(self, event):
        """إغلاق التطبيق"""
        event.accept()


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setApplicationName("مشغل الوسائط المتعددة - تجريبي")
    app.setApplicationVersion("1.0-demo")
    
    # تطبيق خط عربي إذا كان متوفراً
    try:
        font = QFont("Segoe UI", 10)
        app.setFont(font)
    except:
        pass
    
    player = MediaPlayerDemo()
    player.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
