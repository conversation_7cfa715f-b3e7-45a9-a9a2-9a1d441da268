@echo off
echo ========================================
echo تثبيت متطلبات مشغل الوسائط المتعددة
echo ========================================
echo.

echo جاري التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تحميل وتثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo جاري تثبيت المتطلبات...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ❌ فشل في تثبيت المتطلبات
    echo يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح!
echo.
echo يمكنك الآن تشغيل التطبيق باستخدام:
echo python media_player.py
echo.
echo أو بناء ملف تنفيذي باستخدام:
echo python build_exe.py
echo.
pause
